import datetime
import os
import pandas as pd
import numpy as np
import requests
import time
from typing import Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from src.data.cache import get_cache
from src.data.models import (
    CompanyNews,
    CompanyNewsResponse,
    YahooFinanceNews,
    YahooFinanceNewsResponse,
    FinancialMetrics,
    FinancialMetricsResponse,
    Price,
    PriceResponse,
    LineItem,
    LineItemResponse,
    InsiderTrade,
    InsiderTradeResponse,
    CompanyFactsResponse,
)

# Global cache instance
_cache = get_cache()


def _save_input_data(agent_name: str, ticker: str, api_method: str, data: Any, metadata: dict | None = None):
    """
    保存API调用的输入数据到全局输入数据管理器

    Args:
        agent_name: 代理名称
        ticker: 股票代码
        api_method: API方法名称
        data: 数据
        metadata: 元数据
    """
    try:
        from src.utils.input_data_manager import get_global_input_data_manager
        manager = get_global_input_data_manager()
        if manager:
            manager.save_api_data(agent_name, ticker, api_method, data, metadata)
    except Exception as e:
        # 静默处理错误，不影响主要功能
        pass


def get_session():
    """Create a requests session with retry logic."""
    session = requests.Session()
    retry_strategy = Retry(
        total=5,  # Total number of retries
        backoff_factor=1,  # Exponential backoff
        status_forcelist=[429, 500, 502, 503, 504],  # Status codes to retry on
        allowed_methods=["GET", "POST"],  # Methods to retry
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session


def get_prices(ticker: str, start_date: str, end_date: str, agent_name: str | None = None) -> list[Price]:
    """Fetch price data from cache or API."""
    # Check cache first
    if cached_data := _cache.get_prices(ticker):
        # Filter cached data by date range and convert to Price objects
        filtered_data = [Price(**price) for price in cached_data if start_date <= price["time"] <= end_date]
        if filtered_data:
            # Save input data if agent_name is provided
            if agent_name:
                _save_input_data(agent_name, ticker, "get_prices", filtered_data, {
                    "start_date": start_date,
                    "end_date": end_date,
                    "source": "cache"
                })
            return filtered_data

    # If not in cache or no data in range, fetch from API
    headers = {}
    if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        headers["X-API-KEY"] = api_key

    url = f"https://api.financialdatasets.ai/prices/?ticker={ticker}&interval=day&interval_multiplier=1&start_date={start_date}&end_date={end_date}"

    session = get_session()
    response = session.get(url, headers=headers, verify=True)
    if response.status_code != 200:
        raise Exception(f"Error fetching data: {ticker} - {response.status_code} - {response.text}")

    # Parse response with Pydantic model
    price_response = PriceResponse(**response.json())
    prices = price_response.prices

    if not prices:
        return []

    # Cache the results as dicts
    _cache.set_prices(ticker, [p.model_dump() for p in prices])

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "get_prices", prices, {
            "start_date": start_date,
            "end_date": end_date,
            "source": "api"
        })

    return prices


def get_current_price(ticker: str, end_date: str) -> float | None:
    """Get the current price for a specific date."""
    try:
        # Get price data for a small window around the end_date
        from datetime import datetime, timedelta
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=7)  # Look back 7 days to ensure we get data
        start_date = start_dt.strftime("%Y-%m-%d")

        prices = get_prices(ticker, start_date, end_date)
        if prices:
            # Return the most recent price (prices are sorted by date descending)
            return prices[0].close
        return None
    except Exception:
        return None


def calculate_price_momentum(ticker: str, end_date: str, lookback_days: int = 20) -> dict:
    """Calculate price momentum metrics for dynamic analysis."""
    try:
        from datetime import datetime, timedelta
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=lookback_days + 10)  # Extra buffer for weekends
        start_date = start_dt.strftime("%Y-%m-%d")

        prices = get_prices(ticker, start_date, end_date)
        if len(prices) < 2:
            return {"momentum": 0, "volatility": 0, "trend": "neutral"}

        # Calculate momentum (price change over period)
        current_price = prices[0].close
        past_price = prices[-1].close if len(prices) > lookback_days else prices[-1].close
        momentum = (current_price - past_price) / past_price * 100

        # Calculate volatility (standard deviation of returns)
        returns = []
        for i in range(len(prices) - 1):
            daily_return = (prices[i].close - prices[i+1].close) / prices[i+1].close
            returns.append(daily_return)

        volatility = np.std(returns) * 100 if returns else 0

        # Determine trend
        if momentum > 5:
            trend = "strong_bullish"
        elif momentum > 1:
            trend = "bullish"
        elif momentum < -5:
            trend = "strong_bearish"
        elif momentum < -1:
            trend = "bearish"
        else:
            trend = "neutral"

        return {
            "momentum": momentum,
            "volatility": volatility,
            "trend": trend,
            "current_price": current_price,
            "lookback_price": past_price
        }
    except Exception:
        return {"momentum": 0, "volatility": 0, "trend": "neutral"}


def get_dynamic_market_context(ticker: str, end_date: str, agent_name: str | None = None) -> dict:
    """
    Get comprehensive dynamic market context for enhanced agent decision-making.
    This function provides real-time market data to complement static financial metrics.
    """
    try:
        # Get current price and momentum
        current_price = get_current_price(ticker, end_date)
        momentum_data = calculate_price_momentum(ticker, end_date, lookback_days=20)

        # Get short-term and long-term momentum
        short_momentum = calculate_price_momentum(ticker, end_date, lookback_days=5)
        long_momentum = calculate_price_momentum(ticker, end_date, lookback_days=60)

        # Calculate relative strength vs market
        # Try free APIs first, then fall back to statistical model
        try:
            # Try to get SPY data from free APIs
            from src.tools.free_market_data import get_free_spy_momentum
            spy_data = get_free_spy_momentum(end_date, lookback_days=20)

            if spy_data.get("data_source") != "fallback_default":
                # Successfully got real SPY data
                spy_momentum = spy_data.get("momentum", 0)
                relative_strength = momentum_data["momentum"] - spy_momentum
                market_benchmark_available = True
                market_benchmark_source = f"free_api_{spy_data.get('data_source', 'unknown')}"
            else:
                # Free APIs failed, use statistical model
                relative_strength = _calculate_alternative_market_relative_strength(
                    ticker, momentum_data, end_date
                )
                market_benchmark_available = True
                market_benchmark_source = "statistical_model"

        except Exception as e:
            print(f"Warning: Market benchmark calculation failed ({e})")
            relative_strength = 0  # Neutral relative strength when no benchmark
            market_benchmark_available = False
            market_benchmark_source = "none"

        # Get recent price levels for support/resistance analysis
        from datetime import datetime, timedelta
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        start_dt = end_dt - timedelta(days=90)
        start_date = start_dt.strftime("%Y-%m-%d")

        prices = get_prices(ticker, start_date, end_date)
        price_levels = {}
        if prices:
            closes = [p.close for p in prices]
            price_levels = {
                "current": current_price,
                "max_90d": max(closes),
                "min_90d": min(closes),
                "avg_90d": sum(closes) / len(closes),
                "distance_from_high": (current_price - max(closes)) / max(closes) * 100 if current_price else 0,
                "distance_from_low": (current_price - min(closes)) / min(closes) * 100 if current_price else 0,
            }

        result = {
            "current_price": current_price,
            "momentum_20d": momentum_data,
            "momentum_5d": short_momentum,
            "momentum_60d": long_momentum,
            "relative_strength_vs_market": relative_strength,
            "market_benchmark_available": market_benchmark_available,
            "market_benchmark_source": market_benchmark_source,
            "price_levels": price_levels,
            "market_sentiment": _determine_market_sentiment(momentum_data, price_levels),
            "trading_date": end_date
        }

        # Save input data if agent_name is provided
        if agent_name:
            _save_input_data(agent_name, ticker, "get_dynamic_market_context", result, {
                "end_date": end_date,
                "source": "dynamic_calculation"
            })

        return result
    except Exception as e:
        return {
            "error": str(e),
            "current_price": None,
            "momentum_20d": {"momentum": 0, "volatility": 0, "trend": "neutral"},
            "relative_strength_vs_market": 0,
            "market_benchmark_available": False,
            "market_benchmark_source": "error",
            "market_sentiment": "neutral",
            "trading_date": end_date
        }


def _calculate_alternative_market_relative_strength(ticker: str, momentum_data: dict, end_date: str) -> float:
    """
    Calculate alternative market relative strength without SPY data.
    Uses statistical models and sector analysis to estimate market performance.
    """
    try:
        # Get the stock's momentum and volatility
        stock_momentum = momentum_data.get("momentum", 0)
        stock_volatility = momentum_data.get("volatility", 0)

        # Sector-based market estimation
        # Different sectors have different typical performance patterns
        sector_adjustments = {
            # Technology stocks (typically higher momentum)
            "AAPL": {"base_market": 1.5, "volatility_factor": 1.2},
            "MSFT": {"base_market": 1.2, "volatility_factor": 1.0},
            "GOOGL": {"base_market": 1.3, "volatility_factor": 1.1},
            "NVDA": {"base_market": 2.0, "volatility_factor": 1.5},
            "TSLA": {"base_market": 1.8, "volatility_factor": 2.0},

            # Financial stocks (typically follow market closely)
            "JPM": {"base_market": 0.8, "volatility_factor": 0.9},
            "BAC": {"base_market": 0.7, "volatility_factor": 0.8},

            # Consumer goods (typically stable)
            "PG": {"base_market": 0.5, "volatility_factor": 0.6},
            "KO": {"base_market": 0.4, "volatility_factor": 0.5},

            # Healthcare (typically defensive)
            "JNJ": {"base_market": 0.6, "volatility_factor": 0.7},
            "PFE": {"base_market": 0.5, "volatility_factor": 0.8},
        }

        # Get sector adjustment or use default
        sector_info = sector_adjustments.get(ticker, {"base_market": 1.0, "volatility_factor": 1.0})

        # Estimate market momentum based on multiple factors
        base_market_momentum = sector_info["base_market"]

        # Adjust based on overall market conditions inferred from stock behavior
        if stock_volatility > 4:  # High volatility suggests uncertain/bearish market
            market_condition_adjustment = -1.5
        elif stock_volatility > 2.5:  # Moderate volatility
            market_condition_adjustment = -0.5
        elif stock_momentum > 8:  # Very strong momentum suggests bull market
            market_condition_adjustment = 2.0
        elif stock_momentum > 3:  # Good momentum suggests positive market
            market_condition_adjustment = 1.0
        elif stock_momentum < -8:  # Very weak suggests bear market
            market_condition_adjustment = -2.5
        elif stock_momentum < -3:  # Weak suggests negative market
            market_condition_adjustment = -1.0
        else:  # Neutral momentum
            market_condition_adjustment = 0.0

        # Calculate estimated market momentum
        estimated_market_momentum = base_market_momentum + market_condition_adjustment

        # Apply volatility factor
        estimated_market_momentum *= sector_info["volatility_factor"]

        # Calculate relative strength
        relative_strength = stock_momentum - estimated_market_momentum

        # Cap the relative strength to reasonable bounds (-20% to +20%)
        relative_strength = max(-20, min(20, relative_strength))

        return relative_strength

    except Exception:
        return 0.0  # Return neutral if calculation fails


def _determine_market_sentiment(momentum_data: dict, price_levels: dict) -> str:
    """Determine overall market sentiment based on momentum and price position."""
    momentum = momentum_data.get("momentum", 0)
    volatility = momentum_data.get("volatility", 0)

    # Check price position relative to recent range
    distance_from_high = price_levels.get("distance_from_high", 0)
    distance_from_low = price_levels.get("distance_from_low", 0)

    # Combine momentum and position analysis
    if momentum > 3 and distance_from_high > -10:  # Strong momentum near highs
        return "very_bullish"
    elif momentum > 1 and distance_from_high > -20:  # Moderate momentum
        return "bullish"
    elif momentum < -3 and distance_from_low < 10:  # Strong decline near lows
        return "very_bearish"
    elif momentum < -1 and distance_from_low < 20:  # Moderate decline
        return "bearish"
    elif volatility > 3:  # High volatility
        return "volatile"
    else:
        return "neutral"


def get_financial_metrics(
    ticker: str,
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
    agent_name: str | None = None,
) -> list[FinancialMetrics]:
    """Fetch financial metrics from cache or API."""
    # Check cache first
    if cached_data := _cache.get_financial_metrics(ticker):
        # Filter cached data by date and limit
        filtered_data = [FinancialMetrics(**metric) for metric in cached_data if metric["report_period"] <= end_date]
        filtered_data.sort(key=lambda x: x.report_period, reverse=True)
        if filtered_data:
            result = filtered_data[:limit]
            # Save input data if agent_name is provided
            if agent_name:
                _save_input_data(agent_name, ticker, "get_financial_metrics", result, {
                    "end_date": end_date,
                    "period": period,
                    "limit": limit,
                    "source": "cache"
                })
            return result

    # If not in cache or insufficient data, fetch from API
    headers = {}
    if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        headers["X-API-KEY"] = api_key

    url = f"https://api.financialdatasets.ai/financial-metrics/?ticker={ticker}&report_period_lte={end_date}&limit={limit}&period={period}"

    session = get_session()
    response = session.get(url, headers=headers, verify=True)
    if response.status_code != 200:
        raise Exception(f"Error fetching data: {ticker} - {response.status_code} - {response.text}")

    # Parse response with Pydantic model
    metrics_response = FinancialMetricsResponse(**response.json())
    # Return the FinancialMetrics objects directly instead of converting to dict
    financial_metrics = metrics_response.financial_metrics

    if not financial_metrics:
        return []

    # Cache the results as dicts
    _cache.set_financial_metrics(ticker, [m.model_dump() for m in financial_metrics])

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "get_financial_metrics", financial_metrics, {
            "end_date": end_date,
            "period": period,
            "limit": limit,
            "source": "api"
        })

    return financial_metrics


def search_line_items(
    ticker: str,
    line_items: list[str],
    end_date: str,
    period: str = "ttm",
    limit: int = 10,
    agent_name: str | None = None,
) -> list[LineItem]:
    """Fetch line items from API."""
    # If not in cache or insufficient data, fetch from API
    headers = {}
    if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        headers["X-API-KEY"] = api_key

    url = "https://api.financialdatasets.ai/financials/search/line-items"

    body = {
        "tickers": [ticker],
        "line_items": line_items,
        "end_date": end_date,
        "period": period,
        "limit": limit,
    }
    
    session = get_session()
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            response = session.post(url, headers=headers, json=body, verify=True)
            if response.status_code != 200:
                raise Exception(f"Error fetching data: {ticker} - {response.status_code} - {response.text}")
            break
        except requests.exceptions.SSLError as e:
            if attempt < max_attempts - 1:  # if not the last attempt
                print(f"SSL error occurred: {e}. Retrying ({attempt+1}/{max_attempts})...")
                time.sleep(2 ** attempt)  # exponential backoff
            else:
                raise
    else:
        raise Exception(f"Failed to fetch data after {max_attempts} attempts")
        
    data = response.json()
    response_model = LineItemResponse(**data)
    search_results = response_model.search_results
    if not search_results:
        return []

    # Cache the results
    result = search_results[:limit]

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "search_line_items", result, {
            "line_items": line_items,
            "end_date": end_date,
            "period": period,
            "limit": limit,
            "source": "api"
        })

    return result


def get_insider_trades(
    ticker: str,
    end_date: str,
    start_date: str | None = None,
    limit: int = 1000,
    agent_name: str | None = None,
) -> list[InsiderTrade]:
    """Fetch insider trades from cache or API."""
    # Check cache first
    if cached_data := _cache.get_insider_trades(ticker):
        # Filter cached data by date range
        filtered_data = [InsiderTrade(**trade) for trade in cached_data if (start_date is None or (trade.get("transaction_date") or trade["filing_date"]) >= start_date) and (trade.get("transaction_date") or trade["filing_date"]) <= end_date]
        filtered_data.sort(key=lambda x: x.transaction_date or x.filing_date, reverse=True)
        if filtered_data:
            return filtered_data

    # If not in cache or insufficient data, fetch from API
    headers = {}
    if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        headers["X-API-KEY"] = api_key

    all_trades = []
    current_end_date = end_date

    while True:
        url = f"https://api.financialdatasets.ai/insider-trades/?ticker={ticker}&filing_date_lte={current_end_date}"
        if start_date:
            url += f"&filing_date_gte={start_date}"
        url += f"&limit={limit}"

        session = get_session()
        response = session.get(url, headers=headers, verify=True)
        if response.status_code != 200:
            raise Exception(f"Error fetching data: {ticker} - {response.status_code} - {response.text}")

        data = response.json()
        response_model = InsiderTradeResponse(**data)
        insider_trades = response_model.insider_trades

        if not insider_trades:
            break

        all_trades.extend(insider_trades)

        # Only continue pagination if we have a start_date and got a full page
        if not start_date or len(insider_trades) < limit:
            break

        # Update end_date to the oldest filing date from current batch for next iteration
        current_end_date = min(trade.filing_date for trade in insider_trades).split("T")[0]

        # If we've reached or passed the start_date, we can stop
        if current_end_date <= start_date:
            break

    if not all_trades:
        return []

    # Cache the results
    _cache.set_insider_trades(ticker, [trade.model_dump() for trade in all_trades])

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "get_insider_trades", all_trades, {
            "end_date": end_date,
            "start_date": start_date,
            "limit": limit,
            "source": "api"
        })

    return all_trades


def get_company_news(
    ticker: str,
    end_date: str,
    start_date: str | None = None,
    limit: int = 1000,
    agent_name: str | None = None,
) -> list[CompanyNews]:
    """Fetch company news from cache or API."""
    # Check cache first
    if cached_data := _cache.get_company_news(ticker):
        # Filter cached data by date range
        filtered_data = [CompanyNews(**news) for news in cached_data if (start_date is None or news["date"] >= start_date) and news["date"] <= end_date]
        filtered_data.sort(key=lambda x: x.date, reverse=True)
        if filtered_data:
            return filtered_data

    # If not in cache or insufficient data, fetch from API
    headers = {}
    if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        headers["X-API-KEY"] = api_key

    all_news = []
    current_end_date = end_date

    while True:
        url = f"https://api.financialdatasets.ai/news/?ticker={ticker}&end_date={current_end_date}"
        if start_date:
            url += f"&start_date={start_date}"
        url += f"&limit={limit}"

        session = get_session()
        response = session.get(url, headers=headers, verify=True)
        if response.status_code != 200:
            raise Exception(f"Error fetching data: {ticker} - {response.status_code} - {response.text}")

        data = response.json()
        response_model = CompanyNewsResponse(**data)
        company_news = response_model.news

        if not company_news:
            break

        all_news.extend(company_news)

        # Only continue pagination if we have a start_date and got a full page
        if not start_date or len(company_news) < limit:
            break

        # Update end_date to the oldest date from current batch for next iteration
        current_end_date = min(news.date for news in company_news).split("T")[0]

        # If we've reached or passed the start_date, we can stop
        if current_end_date <= start_date:
            break

    if not all_news:
        return []

    # Cache the results
    _cache.set_company_news(ticker, [news.model_dump() for news in all_news])

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "get_company_news", all_news, {
            "end_date": end_date,
            "start_date": start_date,
            "limit": limit,
            "source": "api"
        })

    return all_news


def get_market_cap(
    ticker: str,
    end_date: str,
) -> float | None:
    """Fetch market cap from the API."""
    # Check if end_date is today
    if end_date == datetime.datetime.now().strftime("%Y-%m-%d"):
        # Get the market cap from company facts API
        headers = {}
        if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
            headers["X-API-KEY"] = api_key

        url = f"https://api.financialdatasets.ai/company/facts/?ticker={ticker}"
        
        session = get_session()
        response = session.get(url, headers=headers, verify=True)
        if response.status_code != 200:
            print(f"Error fetching company facts: {ticker} - {response.status_code}")
            return None

        data = response.json()
        response_model = CompanyFactsResponse(**data)
        return response_model.company_facts.market_cap

    financial_metrics = get_financial_metrics(ticker, end_date)
    if not financial_metrics:
        return None

    market_cap = financial_metrics[0].market_cap

    if not market_cap:
        return None

    return market_cap


def prices_to_df(prices: list[Price]) -> pd.DataFrame:
    """Convert prices to a DataFrame."""
    df = pd.DataFrame([p.model_dump() for p in prices])
    df["Date"] = pd.to_datetime(df["time"])
    df.set_index("Date", inplace=True)
    numeric_cols = ["open", "close", "high", "low", "volume"]
    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors="coerce")
    df.sort_index(inplace=True)
    return df


# Update the get_price_data function to use the new functions
def get_price_data(ticker: str, start_date: str, end_date: str) -> pd.DataFrame:
    prices = get_prices(ticker, start_date, end_date)
    return prices_to_df(prices)


def get_yahoo_finance_news(
    ticker: str,
    start_date: str | None = None,
    end_date: str | None = None,
    limit: int = 50,
) -> list[YahooFinanceNews]:
    """
    从Yahoo Finance获取与股价相关的公司新闻。

    Args:
        ticker: 股票代码 (例如: "AAPL", "MSFT")
        start_date: 开始日期，格式为 "YYYY-MM-DD"，可选
        end_date: 结束日期，格式为 "YYYY-MM-DD"，可选
        limit: 返回新闻文章的最大数量，默认50

    Returns:
        list[YahooFinanceNews]: Yahoo Finance新闻文章列表

    Raises:
        Exception: 当API调用失败或数据解析错误时

    Examples:
        >>> news = get_yahoo_finance_news("AAPL", limit=10)
        >>> for article in news:
        ...     print(f"{article.title} - {article.date}")

        >>> recent_news = get_yahoo_finance_news(
        ...     "MSFT",
        ...     start_date="2024-01-01",
        ...     end_date="2024-01-31"
        ... )
    """
    try:
        import yfinance as yf
        from datetime import datetime, timedelta
        import re

        # 检查缓存
        if cached_data := _cache.get_yahoo_finance_news(ticker):
            # 过滤缓存数据
            filtered_cached = []
            for news_dict in cached_data:
                news_date = news_dict.get("date", "")
                if start_date and news_date < start_date:
                    continue
                if end_date and news_date > end_date:
                    continue
                filtered_cached.append(YahooFinanceNews(**news_dict))

            if filtered_cached and len(filtered_cached) >= min(limit, 10):
                return filtered_cached[:limit]

        # 创建yfinance股票对象
        stock = yf.Ticker(ticker)

        # 获取新闻数据
        try:
            news_data = stock.news
        except Exception as e:
            print(f"Warning: Failed to fetch news for {ticker}: {e}")
            return []

        if not news_data:
            return []

        # 处理日期过滤
        filtered_news = []
        current_time = datetime.now()

        for article in news_data[:limit * 2]:  # 获取更多数据以便过滤
            try:
                # 转换时间戳为日期字符串
                if 'providerPublishTime' in article:
                    publish_time = datetime.fromtimestamp(article['providerPublishTime'])
                    article_date = publish_time.strftime("%Y-%m-%d")
                else:
                    # 如果没有时间戳，使用当前日期
                    article_date = current_time.strftime("%Y-%m-%d")

                # 应用日期过滤
                if start_date and article_date < start_date:
                    continue
                if end_date and article_date > end_date:
                    continue

                # 提取和清理内容
                title = article.get('title', '').strip()
                summary = article.get('summary', '').strip() if article.get('summary') else None

                # 清理HTML标签（如果存在）
                if summary:
                    summary = re.sub(r'<[^>]+>', '', summary)

                # 确定新闻来源
                source = article.get('publisher', 'Yahoo Finance')

                # 构建新闻对象
                yahoo_news = YahooFinanceNews(
                    ticker=ticker.upper(),
                    title=title,
                    summary=summary,
                    content=summary,  # Yahoo Finance通常只提供摘要
                    author=None,  # Yahoo Finance API通常不提供作者信息
                    source=source,
                    date=article_date,
                    url=article.get('link', ''),
                    sentiment=None,  # 需要单独的情感分析
                    thumbnail=article.get('thumbnail', {}).get('resolutions', [{}])[0].get('url') if article.get('thumbnail') else None
                )

                filtered_news.append(yahoo_news)

                # 达到限制数量时停止
                if len(filtered_news) >= limit:
                    break

            except Exception as e:
                print(f"Warning: Error processing news article for {ticker}: {e}")
                continue

        # 按日期排序（最新的在前）
        filtered_news.sort(key=lambda x: x.date, reverse=True)

        # 缓存结果
        if filtered_news:
            _cache.set_yahoo_finance_news(ticker, [news.model_dump() for news in filtered_news])

        return filtered_news

    except ImportError:
        raise Exception(
            "yfinance library is required for Yahoo Finance news. "
            "Please install it with: pip install yfinance"
        )
    except Exception as e:
        print(f"Error fetching Yahoo Finance news for {ticker}: {e}")
        return []


def format_yahoo_news_for_agents(news_list: list[YahooFinanceNews]) -> str:
    """
    将Yahoo Finance新闻列表格式化为适合新闻代理分析的文本格式。

    Args:
        news_list: Yahoo Finance新闻文章列表

    Returns:
        str: 格式化的新闻文本，可直接传递给factual_news_agent和subjective_news_agent

    Examples:
        >>> news = get_yahoo_finance_news("AAPL", limit=5)
        >>> formatted_text = format_yahoo_news_for_agents(news)
        >>> # 现在可以将formatted_text传递给新闻代理进行分析
    """
    if not news_list:
        return "No news articles available for analysis."

    formatted_articles = []

    for i, article in enumerate(news_list, 1):
        article_text = f"Article {i}:\n"
        article_text += f"Title: {article.title}\n"
        article_text += f"Source: {article.source}\n"
        article_text += f"Date: {article.date}\n"

        if article.summary:
            article_text += f"Summary: {article.summary}\n"
        elif article.content:
            article_text += f"Content: {article.content}\n"

        if article.url:
            article_text += f"URL: {article.url}\n"

        article_text += "-" * 50 + "\n"
        formatted_articles.append(article_text)

    return "\n".join(formatted_articles)


def get_formatted_yahoo_news(
    ticker: str,
    start_date: str | None = None,
    end_date: str | None = None,
    limit: int = 10,
) -> str:
    """
    获取Yahoo Finance新闻并直接返回格式化的文本，可直接用于新闻代理分析。

    这是一个便利函数，结合了get_yahoo_finance_news和format_yahoo_news_for_agents。

    Args:
        ticker: 股票代码
        start_date: 开始日期，可选
        end_date: 结束日期，可选
        limit: 新闻文章数量限制，默认10

    Returns:
        str: 格式化的新闻文本，可直接传递给新闻代理

    Examples:
        >>> formatted_news = get_formatted_yahoo_news("AAPL", limit=5)
        >>> # 可以直接在状态中使用：
        >>> state["data"]["news"] = formatted_news
    """
    news_list = get_yahoo_finance_news(ticker, start_date, end_date, limit)
    return format_yahoo_news_for_agents(news_list)


def get_alpha_vantage_news(
    ticker: str,
    limit: int = 10,
    api_key: str | None = None,
    date: str | None = None,
) -> list[YahooFinanceNews]:
    """
    从Alpha Vantage获取股票新闻数据。

    Args:
        ticker: 股票代码
        limit: 返回新闻数量限制
        api_key: Alpha Vantage API密钥，如果为None则从环境变量获取
        date: 目标日期 (YYYY-MM-DD格式)，用于缓存键和日期过滤

    Returns:
        list[YahooFinanceNews]: 新闻文章列表
    """
    if api_key is None:
        api_key = os.environ.get("ALPHA_VANTAGE_API_KEY")

    if not api_key:
        print("Warning: ALPHA_VANTAGE_API_KEY not found in environment variables")
        return []

    try:
        # 检查缓存 - 包含日期信息
        from datetime import datetime
        cache_date = date if date else datetime.now().strftime("%Y-%m-%d")
        cache_key = f"alpha_vantage_{ticker}_{cache_date}"

        if cached_data := _cache.get_yahoo_finance_news(cache_key):
            cached_news = [YahooFinanceNews(**news_dict) for news_dict in cached_data[:limit]]
            if cached_news:
                print(f"  📋 使用Alpha Vantage缓存数据 ({cache_date})")
                return cached_news

        # Alpha Vantage News & Sentiment API
        url = "https://www.alphavantage.co/query"
        params = {
            "function": "NEWS_SENTIMENT",
            "tickers": ticker,
            "apikey": api_key,
            "limit": min(limit, 50)  # Alpha Vantage限制
        }

        # 添加日期过滤（如果提供）
        if date:
            # Alpha Vantage 支持 time_from 和 time_to 参数
            params["time_from"] = f"{date}T0000"
            params["time_to"] = f"{date}T2359"

        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()

        news_list = []

        if "feed" in data:
            for article in data["feed"][:limit]:
                try:
                    # 转换为统一格式
                    news_item = YahooFinanceNews(
                        ticker=ticker.upper(),
                        title=article.get("title", ""),
                        summary=article.get("summary", ""),
                        content=article.get("summary", ""),  # Alpha Vantage主要提供摘要
                        author=", ".join(article.get("authors", [])) if article.get("authors") else None,
                        source=article.get("source", "Alpha Vantage"),
                        date=article.get("time_published", "")[:10] if article.get("time_published") else "",
                        url=article.get("url", ""),
                        sentiment=None,  # 可以从overall_sentiment_score计算
                        thumbnail=article.get("banner_image")
                    )
                    news_list.append(news_item)

                except Exception as e:
                    print(f"Warning: Error processing Alpha Vantage article: {e}")
                    continue

        # 缓存结果
        if news_list:
            _cache.set_yahoo_finance_news(cache_key, [news.model_dump() for news in news_list])

        return news_list

    except Exception as e:
        print(f"Error fetching Alpha Vantage news for {ticker}: {e}")
        return []


def get_newsapi_financial_news(
    ticker: str,
    limit: int = 10,
    api_key: str | None = None,
    date: str | None = None,
) -> list[YahooFinanceNews]:
    """
    从NewsAPI获取金融新闻数据。

    Args:
        ticker: 股票代码
        limit: 返回新闻数量限制
        api_key: NewsAPI密钥，如果为None则从环境变量获取
        date: 目标日期 (YYYY-MM-DD格式)，用于缓存键和日期过滤

    Returns:
        list[YahooFinanceNews]: 新闻文章列表
    """
    if api_key is None:
        api_key = os.environ.get("NEWSAPI_KEY")

    if not api_key:
        print("Warning: NEWSAPI_KEY not found in environment variables")
        return []

    try:
        # 检查缓存 - 包含日期信息
        from datetime import datetime
        cache_date = date if date else datetime.now().strftime("%Y-%m-%d")
        cache_key = f"newsapi_{ticker}_{cache_date}"

        if cached_data := _cache.get_yahoo_finance_news(cache_key):
            cached_news = [YahooFinanceNews(**news_dict) for news_dict in cached_data[:limit]]
            if cached_news:
                print(f"  📋 使用NewsAPI缓存数据 ({cache_date})")
                return cached_news

        # 获取公司名称映射
        company_names = {
            "AAPL": "Apple",
            "MSFT": "Microsoft",
            "GOOGL": "Google",
            "AMZN": "Amazon",
            "TSLA": "Tesla",
            "META": "Meta",
            "NVDA": "NVIDIA"
        }

        company_name = company_names.get(ticker.upper(), ticker)

        # NewsAPI搜索
        url = "https://newsapi.org/v2/everything"
        params = {
            "q": f"{company_name} OR {ticker}",
            "domains": "finance.yahoo.com,bloomberg.com,reuters.com,cnbc.com,marketwatch.com",
            "language": "en",
            "sortBy": "publishedAt",
            "pageSize": min(limit, 100),
            "apiKey": api_key
        }

        # 添加日期过滤（如果提供）
        if date:
            params["from"] = date
            params["to"] = date

        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()

        news_list = []

        if data.get("status") == "ok" and "articles" in data:
            for article in data["articles"][:limit]:
                try:
                    # 转换为统一格式
                    news_item = YahooFinanceNews(
                        ticker=ticker.upper(),
                        title=article.get("title", ""),
                        summary=article.get("description", ""),
                        content=article.get("content", ""),
                        author=article.get("author"),
                        source=article.get("source", {}).get("name", "NewsAPI"),
                        date=article.get("publishedAt", "")[:10] if article.get("publishedAt") else "",
                        url=article.get("url", ""),
                        sentiment=None,
                        thumbnail=article.get("urlToImage")
                    )
                    news_list.append(news_item)

                except Exception as e:
                    print(f"Warning: Error processing NewsAPI article: {e}")
                    continue

        # 缓存结果
        if news_list:
            _cache.set_yahoo_finance_news(cache_key, [news.model_dump() for news in news_list])

        return news_list

    except Exception as e:
        print(f"Error fetching NewsAPI news for {ticker}: {e}")
        return []


def get_finnhub_news(
    ticker: str,
    limit: int = 10,
    api_key: str | None = None,
    date: str | None = None,
) -> list[YahooFinanceNews]:
    """
    从Finnhub获取股票新闻数据。

    Args:
        ticker: 股票代码
        limit: 返回新闻数量限制
        api_key: Finnhub API密钥，如果为None则从环境变量获取
        date: 目标日期 (YYYY-MM-DD格式)，用于缓存键和日期过滤

    Returns:
        list[YahooFinanceNews]: 新闻文章列表
    """
    if api_key is None:
        api_key = os.environ.get("FINNHUB_API_KEY")

    if not api_key:
        print("Warning: FINNHUB_API_KEY not found in environment variables")
        return []

    try:
        # 检查缓存 - 包含日期信息
        from datetime import datetime
        cache_date = date if date else datetime.now().strftime("%Y-%m-%d")
        cache_key = f"finnhub_{ticker}_{cache_date}"

        if cached_data := _cache.get_yahoo_finance_news(cache_key):
            cached_news = [YahooFinanceNews(**news_dict) for news_dict in cached_data[:limit]]
            if cached_news:
                print(f"  📋 使用Finnhub缓存数据 ({cache_date})")
                return cached_news

        # Finnhub Company News API
        from datetime import datetime, timedelta

        # 设置日期范围
        if date:
            # 使用指定日期
            start_date_str = date
            end_date_str = date
        else:
            # 获取最近7天的新闻
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")

        url = "https://finnhub.io/api/v1/company-news"
        params = {
            "symbol": ticker.upper(),
            "from": start_date_str,
            "to": end_date_str,
            "token": api_key
        }

        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        data = response.json()

        news_list = []

        if isinstance(data, list):
            for article in data[:limit]:
                try:
                    # 转换时间戳为日期
                    timestamp = article.get("datetime", 0)
                    date_str = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d") if timestamp else ""

                    # 转换为统一格式
                    news_item = YahooFinanceNews(
                        ticker=ticker.upper(),
                        title=article.get("headline", ""),
                        summary=article.get("summary", ""),
                        content=article.get("summary", ""),
                        author=None,  # Finnhub通常不提供作者信息
                        source=article.get("source", "Finnhub"),
                        date=date_str,
                        url=article.get("url", ""),
                        sentiment=None,
                        thumbnail=article.get("image")
                    )
                    news_list.append(news_item)

                except Exception as e:
                    print(f"Warning: Error processing Finnhub article: {e}")
                    continue

        # 缓存结果
        if news_list:
            _cache.set_yahoo_finance_news(cache_key, [news.model_dump() for news in news_list])

        return news_list

    except Exception as e:
        print(f"Error fetching Finnhub news for {ticker}: {e}")
        return []


def get_multi_source_news(
    ticker: str,
    limit: int = 10,
    sources: list[str] | None = None,
    fallback_to_mock: bool = True,
    date: str | None = None,
) -> list[YahooFinanceNews]:
    """
    从多个新闻源获取金融新闻，自动处理API限制和错误。

    Args:
        ticker: 股票代码
        limit: 总新闻数量限制
        sources: 要使用的新闻源列表，默认为可靠的API源（不包含Yahoo Finance）
        fallback_to_mock: 当所有API都失败时是否使用模拟数据
        date: 目标日期 (YYYY-MM-DD格式)，用于获取特定日期的新闻

    Returns:
        list[YahooFinanceNews]: 聚合的新闻文章列表
    """
    if sources is None:
        sources = ["alpha_vantage", "newsapi", "finnhub"]  # 移除yahoo，避免速率限制

    all_news = []
    per_source_limit = max(1, limit // len(sources))

    # 新闻源获取函数映射
    source_functions = {
        "yahoo": get_yahoo_finance_news,
        "alpha_vantage": get_alpha_vantage_news,
        "newsapi": get_newsapi_financial_news,
        "finnhub": get_finnhub_news,
    }

    print(f"🔍 从 {len(sources)} 个新闻源获取 {ticker} 的新闻...")

    for source in sources:
        if source not in source_functions:
            print(f"⚠️  未知新闻源: {source}")
            continue

        try:
            print(f"  📰 尝试 {source}...")

            # 调用对应的新闻获取函数，传递日期参数
            if source == "yahoo":
                # Yahoo Finance 函数需要特殊处理日期参数
                if date:
                    from datetime import datetime, timedelta
                    start_date = date
                    end_date = (datetime.strptime(date, "%Y-%m-%d") + timedelta(days=1)).strftime("%Y-%m-%d")
                    source_news = source_functions[source](ticker, start_date, end_date, per_source_limit)
                else:
                    source_news = source_functions[source](ticker, limit=per_source_limit)
            else:
                # 其他新闻源函数
                source_news = source_functions[source](ticker, per_source_limit, date=date)

            if source_news:
                print(f"  ✅ {source}: 获取到 {len(source_news)} 条新闻")
                all_news.extend(source_news)
            else:
                print(f"  ⚠️  {source}: 未获取到新闻")

        except Exception as e:
            error_msg = str(e)
            if "Rate limited" in error_msg or "Too Many Requests" in error_msg:
                print(f"  ⚠️  {source}: 遇到速率限制")
            else:
                print(f"  ❌ {source}: 获取失败 - {e}")

    # 去重和排序
    if all_news:
        # 按URL去重
        seen_urls = set()
        unique_news = []

        for news in all_news:
            if news.url and news.url not in seen_urls:
                seen_urls.add(news.url)
                unique_news.append(news)
            elif not news.url:  # 没有URL的新闻也保留
                unique_news.append(news)

        # 按日期排序（最新的在前）
        unique_news.sort(key=lambda x: x.date, reverse=True)

        # 限制总数量
        result = unique_news[:limit]
        print(f"✅ 总共获取到 {len(result)} 条去重后的新闻")
        return result

    # 如果所有源都失败，使用模拟数据
    if fallback_to_mock:
        print("⚠️  所有新闻源都失败，使用模拟数据")
        return get_mock_news_data(ticker, limit, date)

    return []


def get_mock_news_data(ticker: str, limit: int = 10, date: str | None = None) -> list[YahooFinanceNews]:
    """
    生成模拟新闻数据作为后备方案。

    Args:
        ticker: 股票代码
        limit: 新闻数量限制
        date: 目标日期 (YYYY-MM-DD格式)，用于生成特定日期的模拟新闻

    Returns:
        list[YahooFinanceNews]: 模拟新闻列表
    """
    from datetime import datetime, timedelta

    # 公司名称映射
    company_names = {
        "AAPL": "Apple Inc.",
        "MSFT": "Microsoft Corporation",
        "GOOGL": "Alphabet Inc.",
        "AMZN": "Amazon.com Inc.",
        "TSLA": "Tesla Inc.",
        "META": "Meta Platforms Inc.",
        "NVDA": "NVIDIA Corporation"
    }

    company_name = company_names.get(ticker.upper(), f"{ticker.upper()} Corporation")

    # 模拟新闻模板
    mock_templates = [
        {
            "title": f"{company_name} Reports Strong Quarterly Results",
            "summary": f"{company_name} announced strong quarterly performance with revenue growth exceeding analyst expectations.",
            "source": "Mock Financial News"
        },
        {
            "title": f"{company_name} Announces Strategic Partnership",
            "summary": f"{company_name} entered into a strategic partnership to expand its market presence and technological capabilities.",
            "source": "Mock Business Wire"
        },
        {
            "title": f"Analysts Upgrade {company_name} Stock Rating",
            "summary": f"Several Wall Street analysts upgraded their rating for {company_name} citing strong fundamentals and growth prospects.",
            "source": "Mock Market Watch"
        },
        {
            "title": f"{company_name} Invests in Innovation and R&D",
            "summary": f"{company_name} announced increased investment in research and development to drive future innovation.",
            "source": "Mock Tech News"
        },
        {
            "title": f"{company_name} Expands Global Operations",
            "summary": f"{company_name} is expanding its global footprint with new facilities and market entries.",
            "source": "Mock Reuters"
        }
    ]

    mock_news = []

    # 使用指定日期或当前日期作为基准
    if date:
        base_date = datetime.strptime(date, "%Y-%m-%d")
    else:
        base_date = datetime.now()

    for i in range(min(limit, len(mock_templates))):
        template = mock_templates[i]
        # 为不同的模拟新闻使用不同的日期偏移
        article_date = (base_date - timedelta(days=i)).strftime("%Y-%m-%d")

        news_item = YahooFinanceNews(
            ticker=ticker.upper(),
            title=template["title"],
            summary=template["summary"],
            content=template["summary"],
            author="Mock Author",
            source=template["source"],
            date=article_date,
            url=f"https://mock-news.com/{ticker.lower()}-{i+1}",
            sentiment=None,
            thumbnail=None
        )
        mock_news.append(news_item)

    return mock_news


def get_formatted_multi_source_news(
    ticker: str,
    limit: int = 10,
    sources: list[str] | None = None,
    date: str | None = None,
    agent_name: str | None = None,
) -> str:
    """
    获取多源新闻并格式化为文本。
    支持本地数据和API数据两种模式。

    Args:
        ticker: 股票代码
        limit: 新闻数量限制
        sources: 新闻源列表
        date: 目标日期 (YYYY-MM-DD格式)，用于获取特定日期的新闻
        agent_name: 代理名称，用于保存输入数据

    Returns:
        str: 格式化的新闻文本
    """
    # 检查是否使用本地数据
    try:
        from src.config.news_config import news_config
        from src.tools.local_news_reader import get_local_multi_source_news

        if news_config.is_using_local_data():
            print(f"📰 使用本地新闻数据模式")

            # 使用本地数据
            news_list = get_local_multi_source_news(
                ticker=ticker,
                limit=limit,
                sources=sources,
                date=date
            )

            if news_list:
                formatted_news = format_yahoo_news_for_agents(news_list)

                # Save input data if agent_name is provided
                if agent_name:
                    _save_input_data(agent_name, ticker, "get_formatted_multi_source_news", {
                        "news_list": news_list,
                        "formatted_news": formatted_news
                    }, {
                        "limit": limit,
                        "sources": sources,
                        "date": date,
                        "source": "local_data"
                    })

                return formatted_news
            else:
                print("⚠️  本地数据不可用，检查是否回退到API")
                # 检查是否允许回退到API
                if not news_config.config["default_settings"].get("fallback_to_api", False):
                    print("❌ 本地数据不可用且未启用API回退")
                    return "No news articles available for analysis."

    except ImportError as e:
        print(f"⚠️  无法导入本地新闻配置，使用API模式: {e}")
    except Exception as e:
        print(f"⚠️  本地新闻数据读取失败，回退到API模式: {e}")

    # 使用原有的API模式
    print(f"📡 使用API新闻数据模式")
    news_list = get_multi_source_news(ticker, limit, sources, date=date)
    formatted_news = format_yahoo_news_for_agents(news_list)

    # Save input data if agent_name is provided
    if agent_name:
        _save_input_data(agent_name, ticker, "get_formatted_multi_source_news", {
            "news_list": news_list,
            "formatted_news": formatted_news
        }, {
            "limit": limit,
            "sources": sources,
            "date": date,
            "source": "api_data"
        })

    return formatted_news
