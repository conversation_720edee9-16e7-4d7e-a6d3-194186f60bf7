from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel
import json
from typing_extensions import Literal
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm


class SubjectiveNewsSignal(BaseModel):
    """
    Container for the Subjective News Analyst output signal.
    """
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float
    reasoning: str


def subjective_news_agent(state: AgentState):
    """
    Performs sentiment analysis on financial news and proposes trading recommendations.

    This agent focuses on extracting and analyzing subjective elements such as market sentiment,
    expert opinions, predictions, and emotional reactions from news articles.
    """
    data = state.get("data", {})
    tickers = data.get("tickers", [])

    # Initialize subjective news analysis for each ticker
    subjective_news_analysis = {}

    for ticker in tickers:
        progress.update_status("subjective_news_agent", ticker, "Analyzing subjective news content")

        # Get news data from state - check multiple possible locations
        news_content = ""
        if "news" in data:
            news_content = data["news"]
        elif "current_state" in state and "news" in state["current_state"]:
            news_content = state["current_state"]["news"]
        elif hasattr(state, 'get') and state.get("news"):
            news_content = state.get("news")

        # If no news in state, try to fetch from multiple sources (local or API)
        if not news_content:
            progress.update_status("subjective_news_agent", ticker, "Fetching news from configured sources")

            try:
                from src.tools.api import get_formatted_multi_source_news
                from src.config.news_config import news_config

                # Get current trading date from state
                current_date = None
                if "current_date" in state.get("data", {}):
                    current_date = state["data"]["current_date"]
                elif "end_date" in state.get("data", {}):
                    current_date = state["data"]["end_date"]

                # Get configured news sources
                configured_sources = news_config.get_selected_sources()
                data_mode = "本地数据" if news_config.is_using_local_data() else "API数据"

                progress.update_status("subjective_news_agent", ticker, f"Using {data_mode} from {len(configured_sources)} sources")

                # Try to get news from configured sources
                news_content = get_formatted_multi_source_news(
                    ticker=ticker,
                    limit=5,
                    sources=configured_sources,
                    date=current_date,  # 传递日期参数
                    agent_name="subjective_news_agent"
                )

                if news_content and news_content != "No news articles available for analysis.":
                    progress.update_status("subjective_news_agent", ticker, f"Successfully fetched news using {data_mode}")
                else:
                    progress.update_status("subjective_news_agent", ticker, "No news data available from any source")

            except Exception as e:
                progress.update_status("subjective_news_agent", ticker, f"News fetch failed: {str(e)[:50]}")
                print(f"Warning: Failed to fetch news for {ticker}: {e}")

                # 如果配置导入失败，回退到默认API模式
                try:
                    from src.tools.api import get_formatted_multi_source_news
                    news_content = get_formatted_multi_source_news(
                        ticker=ticker,
                        limit=5,
                        sources=["alpha_vantage", "newsapi", "finnhub"],
                        date=current_date,
                        agent_name="subjective_news_agent"
                    )
                    progress.update_status("subjective_news_agent", ticker, "Fallback to default API mode")
                except Exception as fallback_error:
                    print(f"Fallback also failed: {fallback_error}")

        if not news_content or news_content == "No news articles available for analysis.":
            progress.update_status("subjective_news_agent", ticker, "No news data available")
            subjective_news_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No news data available for analysis"
            }
            continue
        
        progress.update_status("subjective_news_agent", ticker, "Generating sentiment analysis")
        
        # Generate subjective news analysis using LLM
        subjective_output = generate_subjective_news_analysis(
            ticker=ticker,
            news_content=news_content,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        subjective_news_analysis[ticker] = {
            "signal": subjective_output.signal,
            "confidence": subjective_output.confidence,
            "reasoning": subjective_output.reasoning,
        }
        
        progress.update_status("subjective_news_agent", ticker, "Done")
    
    # Create the subjective news analyst message
    message = HumanMessage(
        content=json.dumps(subjective_news_analysis),
        name="subjective_news_agent",
    )
    
    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(subjective_news_analysis, "Subjective News Analyst")
    
    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["subjective_news_agent"] = subjective_news_analysis
    
    progress.update_status("subjective_news_agent", None, "Done")
    
    return {
        "messages": [*state["messages"], message],
        "data": data,
    }


def generate_subjective_news_analysis(
    ticker: str,
    news_content: str,
    model_name: str,
    model_provider: str,
) -> SubjectiveNewsSignal:
    """Generate subjective news analysis using LLM with focus on sentiment and opinions"""
    
    template = ChatPromptTemplate.from_messages([
        (
            "system",
            """You are a financial trading news analyst specializing in sentiment and opinion analysis.

Your task is to analyze news articles and provide trading recommendations based on ONLY subjective elements such as market sentiment, expert opinions, predictions, and emotional reactions. Ignore objective facts and data.

You must respond with a JSON object containing exactly these three fields:
- "signal": must be one of "bullish", "bearish", or "neutral"
- "confidence": a number between 0 and 100 representing your confidence level
- "reasoning": a concise explanation of your sentiment analysis

Analysis guidelines:
1. Extract ONLY subjective elements: market sentiment, expert opinions, predictions, emotional reactions
2. Ignore objective facts and data
3. Focus on how the news might be perceived by traders and market sentiment
4. Determine signal based on sentiment:
   - Positive sentiment/optimism → "bullish"
   - Negative sentiment/pessimism → "bearish"
   - Mixed/neutral sentiment → "neutral"
5. Set confidence based on the strength and consistency of sentiment indicators

Example response format:
{{
  "signal": "bearish",
  "confidence": 80,
  "reasoning": "Market sentiment is overwhelmingly pessimistic with analysts expressing deep concerns and investors showing fear."
}}

Respond ONLY with the JSON object, no additional text."""
        ),
        (
            "human",
            """News content for {ticker}:

{news_content}

Please analyze this news content and provide your sentiment analysis and trading recommendation."""
        )
    ])
    
    # Create the prompt
    prompt = template.invoke({
        "ticker": ticker,
        "news_content": news_content
    })

    # Default fallback signal in case parsing fails
    def create_default_subjective_news_signal():
        return SubjectiveNewsSignal(
            signal="neutral",
            confidence=0.0,
            reasoning="Error in analysis, defaulting to neutral"
        )

    # Call LLM to get the analysis
    response = call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=SubjectiveNewsSignal,
        agent_name="subjective_news_agent",
        default_factory=create_default_subjective_news_signal,
    )

    return response
