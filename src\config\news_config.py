#!/usr/bin/env python3
"""
新闻数据配置管理
支持本地新闻数据源选择和配置
"""

import os
import json
import argparse
from typing import Dict, List, Optional, Union
from pathlib import Path


class NewsConfig:
    """新闻数据配置管理器"""
    
    def __init__(self):
        self.config = {
            # 本地新闻数据目录配置
            "local_data_directories": {
                "alpha_vantage": "AAPL_alpha_news",
                "newsapi": "AAPL_news_api_news", 
                "finnhub": "AAPL_finnhub_news"
            },
            
            # 新闻源配置
            "available_sources": {
                "alpha_vantage": {
                    "name": "Alpha Vantage",
                    "description": "Alpha Vantage新闻数据，包含情感分析",
                    "file_pattern": "alpha_news_{date}.json",
                    "enabled": True
                },
                "newsapi": {
                    "name": "NewsAPI",
                    "description": "NewsAPI新闻数据，覆盖面广",
                    "file_pattern": "news_api_{date}.json",
                    "enabled": True
                },
                "finnhub": {
                    "name": "Finnhub",
                    "description": "Finnhub新闻数据，实时性强",
                    "file_pattern": "finnhub_{date}.json",
                    "enabled": True
                }
            },
            
            # 默认设置
            "default_settings": {
                "use_local_data": True,
                "time_offset_days": 1,  # 使用前一天的新闻数据
                "max_articles_per_source": 10,
                "fallback_to_api": False,  # 本地数据不可用时是否回退到API
                "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
            }
        }
        
        # 从环境变量或配置文件加载设置
        self._load_user_config()
    
    def _load_user_config(self):
        """从环境变量或配置文件加载用户配置"""
        # 检查环境变量
        if os.environ.get("NEWS_USE_LOCAL_DATA"):
            self.config["default_settings"]["use_local_data"] = \
                os.environ.get("NEWS_USE_LOCAL_DATA").lower() == "true"
        
        if os.environ.get("NEWS_SOURCES"):
            sources = os.environ.get("NEWS_SOURCES").split(",")
            self.config["default_settings"]["selected_sources"] = \
                [s.strip() for s in sources if s.strip() in self.config["available_sources"]]
        
        if os.environ.get("NEWS_TIME_OFFSET_DAYS"):
            try:
                offset = int(os.environ.get("NEWS_TIME_OFFSET_DAYS"))
                self.config["default_settings"]["time_offset_days"] = offset
            except ValueError:
                pass
        
        # 检查配置文件
        config_file = Path("news_config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(user_config)
            except Exception as e:
                print(f"警告：无法加载配置文件 {config_file}: {e}")
    
    def _merge_config(self, user_config: Dict):
        """合并用户配置"""
        if "default_settings" in user_config:
            self.config["default_settings"].update(user_config["default_settings"])
        
        if "local_data_directories" in user_config:
            self.config["local_data_directories"].update(user_config["local_data_directories"])
    
    def get_selected_sources(self) -> List[str]:
        """获取选中的新闻源"""
        return self.config["default_settings"]["selected_sources"]
    
    def set_selected_sources(self, sources: List[str]):
        """设置选中的新闻源"""
        valid_sources = [s for s in sources if s in self.config["available_sources"]]
        self.config["default_settings"]["selected_sources"] = valid_sources
    
    def get_local_data_directory(self, source: str) -> Optional[str]:
        """获取指定新闻源的本地数据目录"""
        return self.config["local_data_directories"].get(source)
    
    def get_time_offset_days(self) -> int:
        """获取时间偏移天数"""
        return self.config["default_settings"]["time_offset_days"]
    
    def set_time_offset_days(self, days: int):
        """设置时间偏移天数"""
        self.config["default_settings"]["time_offset_days"] = days
    
    def is_using_local_data(self) -> bool:
        """是否使用本地数据"""
        return self.config["default_settings"]["use_local_data"]
    
    def set_use_local_data(self, use_local: bool):
        """设置是否使用本地数据"""
        self.config["default_settings"]["use_local_data"] = use_local
    
    def get_available_sources(self) -> Dict[str, Dict]:
        """获取可用的新闻源信息"""
        return self.config["available_sources"]
    
    def check_local_data_availability(self) -> Dict[str, Dict]:
        """检查本地数据可用性"""
        availability = {}
        
        for source, info in self.config["available_sources"].items():
            directory = self.get_local_data_directory(source)
            availability[source] = {
                "name": info["name"],
                "directory": directory,
                "exists": False,
                "file_count": 0,
                "latest_date": None,
                "status": "❌ 目录不存在"
            }
            
            if directory and Path(directory).exists():
                dir_path = Path(directory)
                json_files = list(dir_path.glob("*.json"))
                availability[source].update({
                    "exists": True,
                    "file_count": len(json_files),
                    "status": f"✅ 可用 ({len(json_files)} 个文件)"
                })
                
                # 找到最新的日期文件
                if json_files:
                    dates = []
                    for file in json_files:
                        try:
                            # 从文件名提取日期
                            if "alpha_news_" in file.name:
                                date_str = file.name.replace("alpha_news_", "").replace(".json", "")
                            elif "news_api_" in file.name:
                                date_str = file.name.replace("news_api_", "").replace(".json", "")
                            elif "finnhub_" in file.name:
                                date_str = file.name.replace("finnhub_", "").replace(".json", "")
                            else:
                                continue
                            dates.append(date_str)
                        except:
                            continue
                    
                    if dates:
                        availability[source]["latest_date"] = max(dates)
        
        return availability
    
    def save_config(self, filename: str = "news_config.json"):
        """保存当前配置到文件"""
        config_to_save = {
            "default_settings": self.config["default_settings"],
            "local_data_directories": self.config["local_data_directories"]
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到 {filename}")
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")


# 全局配置实例
news_config = NewsConfig()


def interactive_news_source_selection() -> List[str]:
    """交互式新闻源选择"""
    print("\n📰 新闻源选择")
    print("=" * 50)
    
    available_sources = news_config.get_available_sources()
    availability = news_config.check_local_data_availability()
    
    print("可用的新闻源：")
    for i, (source_key, source_info) in enumerate(available_sources.items(), 1):
        status = availability[source_key]["status"]
        print(f"{i}. {source_info['name']} - {source_info['description']}")
        print(f"   状态: {status}")
        if availability[source_key]["latest_date"]:
            print(f"   最新数据: {availability[source_key]['latest_date']}")
        print()
    
    print("选择选项：")
    print("a. 使用所有可用源")
    print("b. 自定义选择")
    print("c. 使用当前配置")
    
    choice = input("请选择 (a/b/c): ").strip().lower()
    
    if choice == 'a':
        # 使用所有有数据的源
        selected = [source for source, avail in availability.items() if avail["exists"]]
        print(f"✅ 已选择所有可用源: {', '.join(selected)}")
        return selected
    
    elif choice == 'b':
        # 自定义选择
        selected = []
        source_list = list(available_sources.keys())
        
        print("\n请选择要使用的新闻源（输入数字，用逗号分隔）：")
        for i, source_key in enumerate(source_list, 1):
            print(f"{i}. {available_sources[source_key]['name']}")
        
        try:
            choices = input("选择 (例如: 1,2,3): ").strip()
            if choices:
                indices = [int(x.strip()) - 1 for x in choices.split(',')]
                selected = [source_list[i] for i in indices if 0 <= i < len(source_list)]
                print(f"✅ 已选择: {', '.join(selected)}")
                return selected
        except ValueError:
            print("❌ 输入格式错误，使用默认配置")
    
    # 使用当前配置
    current = news_config.get_selected_sources()
    print(f"✅ 使用当前配置: {', '.join(current)}")
    return current


def setup_news_config_from_args(args: argparse.Namespace):
    """从命令行参数设置新闻配置"""
    if hasattr(args, 'news_sources') and args.news_sources:
        sources = [s.strip() for s in args.news_sources.split(',')]
        news_config.set_selected_sources(sources)
        print(f"📰 新闻源设置为: {', '.join(sources)}")
    
    if hasattr(args, 'news_time_offset') and args.news_time_offset is not None:
        news_config.set_time_offset_days(args.news_time_offset)
        print(f"⏰ 时间偏移设置为: {args.news_time_offset} 天")
    
    if hasattr(args, 'use_local_news') and args.use_local_news is not None:
        news_config.set_use_local_data(args.use_local_news)
        mode = "本地数据" if args.use_local_news else "API数据"
        print(f"📊 数据模式设置为: {mode}")


def add_news_config_args(parser: argparse.ArgumentParser):
    """为ArgumentParser添加新闻配置参数"""
    news_group = parser.add_argument_group('新闻配置')
    
    news_group.add_argument(
        '--news-sources',
        type=str,
        help='指定新闻源，用逗号分隔 (alpha_vantage,newsapi,finnhub)'
    )
    
    news_group.add_argument(
        '--news-time-offset',
        type=int,
        help='时间偏移天数，用于获取指定天数前的新闻 (默认: 1)'
    )
    
    news_group.add_argument(
        '--use-local-news',
        action='store_true',
        help='使用本地新闻数据而不是API'
    )
    
    news_group.add_argument(
        '--interactive-news-setup',
        action='store_true',
        help='交互式新闻源选择'
    )


def print_news_config_status():
    """打印新闻配置状态"""
    print("\n📰 新闻数据配置状态")
    print("=" * 50)
    
    print(f"数据模式: {'本地数据' if news_config.is_using_local_data() else 'API数据'}")
    print(f"时间偏移: {news_config.get_time_offset_days()} 天")
    print(f"选中的源: {', '.join(news_config.get_selected_sources())}")
    
    print("\n📊 本地数据可用性:")
    availability = news_config.check_local_data_availability()
    
    for source, info in availability.items():
        print(f"\n{info['name']}:")
        print(f"  目录: {info['directory']}")
        print(f"  状态: {info['status']}")
        if info['latest_date']:
            print(f"  最新数据: {info['latest_date']}")


if __name__ == "__main__":
    print_news_config_status()
