from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel
import json
from typing_extensions import Literal
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm


class FactualNewsSignal(BaseModel):
    """
    Container for the Factual News Analyst output signal.
    """
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float
    reasoning: str


def factual_news_agent(state: AgentState):
    """
    Analyzes news articles and proposes financial trading recommendations based on objective facts.

    This agent focuses on extracting and analyzing only objective facts, events, and data
    from news articles, ignoring opinions, predictions, and subjective judgments.
    """
    data = state.get("data", {})
    tickers = data.get("tickers", [])

    # Initialize factual news analysis for each ticker
    factual_news_analysis = {}

    for ticker in tickers:
        progress.update_status("factual_news_agent", ticker, "Analyzing factual news content")

        # Get news data from state - check multiple possible locations
        news_content = ""
        if "news" in data:
            news_content = data["news"]
        elif "current_state" in state and "news" in state["current_state"]:
            news_content = state["current_state"]["news"]
        elif hasattr(state, 'get') and state.get("news"):
            news_content = state.get("news")

        # If no news in state, try to fetch from multiple sources (local or API)
        if not news_content:
            progress.update_status("factual_news_agent", ticker, "Fetching news from configured sources")

            try:
                from src.tools.api import get_formatted_multi_source_news
                from src.config.news_config import news_config

                # Get current trading date from state
                current_date = None
                if "current_date" in state.get("data", {}):
                    current_date = state["data"]["current_date"]
                elif "end_date" in state.get("data", {}):
                    current_date = state["data"]["end_date"]

                # Get configured news sources
                configured_sources = news_config.get_selected_sources()
                data_mode = "本地数据" if news_config.is_using_local_data() else "API数据"

                progress.update_status("factual_news_agent", ticker, f"Using {data_mode} from {len(configured_sources)} sources")

                # Try to get news from configured sources
                news_content = get_formatted_multi_source_news(
                    ticker=ticker,
                    limit=5,
                    sources=configured_sources,
                    date=current_date,  # 传递日期参数
                    agent_name="factual_news_agent"
                )

                if news_content and news_content != "No news articles available for analysis.":
                    progress.update_status("factual_news_agent", ticker, f"Successfully fetched news using {data_mode}")
                else:
                    progress.update_status("factual_news_agent", ticker, "No news data available from any source")

            except Exception as e:
                progress.update_status("factual_news_agent", ticker, f"News fetch failed: {str(e)[:50]}")
                print(f"Warning: Failed to fetch news for {ticker}: {e}")

                # 如果配置导入失败，回退到默认API模式
                try:
                    from src.tools.api import get_formatted_multi_source_news
                    news_content = get_formatted_multi_source_news(
                        ticker=ticker,
                        limit=5,
                        sources=["alpha_vantage", "newsapi", "finnhub"],
                        date=current_date,
                        agent_name="factual_news_agent"
                    )
                    progress.update_status("factual_news_agent", ticker, "Fallback to default API mode")
                except Exception as fallback_error:
                    print(f"Fallback also failed: {fallback_error}")

        if not news_content or news_content == "No news articles available for analysis.":
            progress.update_status("factual_news_agent", ticker, "No news data available")
            factual_news_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": "No news data available for analysis"
            }
            continue
        
        progress.update_status("factual_news_agent", ticker, "Generating factual analysis")
        
        # Generate factual news analysis using LLM
        factual_output = generate_factual_news_analysis(
            ticker=ticker,
            news_content=news_content,
            model_name=state["metadata"]["model_name"],
            model_provider=state["metadata"]["model_provider"],
        )
        
        # Store analysis in consistent format with other agents
        factual_news_analysis[ticker] = {
            "signal": factual_output.signal,
            "confidence": factual_output.confidence,
            "reasoning": factual_output.reasoning,
        }
        
        progress.update_status("factual_news_agent", ticker, "Done")
    
    # Create the factual news analyst message
    message = HumanMessage(
        content=json.dumps(factual_news_analysis),
        name="factual_news_agent",
    )
    
    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(factual_news_analysis, "Factual News Analyst")
    
    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["factual_news_agent"] = factual_news_analysis
    
    progress.update_status("factual_news_agent", None, "Done")
    
    return {
        "messages": [*state["messages"], message],
        "data": data,
    }


def generate_factual_news_analysis(
    ticker: str,
    news_content: str,
    model_name: str,
    model_provider: str,
) -> FactualNewsSignal:
    """Generate factual news analysis using LLM with focus on objective information"""
    
    template = ChatPromptTemplate.from_messages([
        (
            "system",
            """You are a financial trading news analyst specializing in factual information extraction.

Your task is to analyze news articles and provide trading recommendations based on ONLY objective facts, events, and data. Ignore opinions, predictions, and subjective judgments.

You must respond with a JSON object containing exactly these three fields:
- "signal": must be one of "bullish", "bearish", or "neutral"
- "confidence": a number between 0 and 100 representing your confidence level
- "reasoning": a concise explanation of your analysis focusing on objective facts

Analysis guidelines:
1. Extract ONLY objective facts, events, and data from the news
2. Ignore opinions, predictions, and subjective judgments
3. Focus on events, announcements, data, and verifiable information
4. Determine signal based on factual impact:
   - Positive factual developments → "bullish"
   - Negative factual developments → "bearish"
   - Neutral/uncertain factual information → "neutral"
5. Set confidence based on the strength and clarity of the factual evidence

Example response format:
{{
  "signal": "bullish",
  "confidence": 75,
  "reasoning": "Company reported 15% revenue growth and announced new product launch with confirmed partnerships."
}}

Respond ONLY with the JSON object, no additional text."""
        ),
        (
            "human",
            """News content for {ticker}:

{news_content}

Please analyze this news content and provide your factual analysis and trading recommendation."""
        )
    ])
    
    # Create the prompt
    prompt = template.invoke({
        "ticker": ticker,
        "news_content": news_content
    })

    # Default fallback signal in case parsing fails
    def create_default_factual_news_signal():
        return FactualNewsSignal(
            signal="neutral",
            confidence=0.0,
            reasoning="Error in analysis, defaulting to neutral"
        )

    # Call LLM to get the analysis
    response = call_llm(
        prompt=prompt,
        model_name=model_name,
        model_provider=model_provider,
        pydantic_model=FactualNewsSignal,
        agent_name="factual_news_agent",
        default_factory=create_default_factual_news_signal,
    )

    return response
