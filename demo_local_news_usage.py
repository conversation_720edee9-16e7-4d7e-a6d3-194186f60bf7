#!/usr/bin/env python3
"""
本地新闻系统使用演示
展示如何使用新的本地新闻数据系统
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def demo_basic_usage():
    """演示基本使用方法"""
    print("📰 演示1: 基本使用方法")
    print("=" * 50)
    
    from src.config.news_config import news_config, print_news_config_status
    from src.tools.local_news_reader import get_local_multi_source_news
    
    # 显示当前配置
    print("📋 当前配置状态:")
    print_news_config_status()
    
    # 获取新闻数据
    print(f"\n📰 获取新闻数据:")
    news_data = get_local_multi_source_news(
        ticker="AAPL",
        limit=5
    )
    
    if news_data:
        print(f"✅ 成功获取 {len(news_data)} 条新闻")
        for i, news in enumerate(news_data[:2], 1):
            print(f"\n📄 新闻 {i}:")
            print(f"  标题: {news.title}")
            print(f"  来源: {news.source}")
            print(f"  日期: {news.date}")
            if news.sentiment:
                print(f"  情感: {news.sentiment}")
    else:
        print("❌ 未获取到新闻数据")


def demo_source_selection():
    """演示新闻源选择"""
    print("\n📰 演示2: 新闻源选择")
    print("=" * 50)
    
    from src.config.news_config import news_config
    from src.tools.local_news_reader import get_local_multi_source_news
    
    # 测试不同的新闻源组合
    source_combinations = [
        ["alpha_vantage"],
        ["newsapi"],
        ["finnhub"],
        ["alpha_vantage", "newsapi"],
        ["alpha_vantage", "newsapi", "finnhub"]
    ]
    
    for sources in source_combinations:
        print(f"\n🔍 测试新闻源: {', '.join(sources)}")
        
        news_data = get_local_multi_source_news(
            ticker="AAPL",
            limit=3,
            sources=sources
        )
        
        if news_data:
            print(f"  ✅ 获取到 {len(news_data)} 条新闻")
            # 统计各源的新闻数量
            source_counts = {}
            for news in news_data:
                source = news.source
                source_counts[source] = source_counts.get(source, 0) + 1
            
            for source, count in source_counts.items():
                print(f"    {source}: {count} 条")
        else:
            print(f"  ❌ 未获取到新闻")


def demo_time_offset():
    """演示时间偏移功能"""
    print("\n📰 演示3: 时间偏移功能")
    print("=" * 50)
    
    from src.config.news_config import news_config
    from src.tools.local_news_reader import local_news_reader, get_local_multi_source_news
    
    # 测试不同的时间偏移
    offsets = [0, 1, 2, 3]
    
    for offset in offsets:
        print(f"\n⏰ 测试时间偏移: {offset} 天")
        
        # 设置时间偏移
        news_config.set_time_offset_days(offset)
        
        # 获取目标日期
        target_date = local_news_reader.get_target_date()
        print(f"  📅 目标日期: {target_date}")
        
        # 获取新闻数据
        news_data = get_local_multi_source_news(
            ticker="AAPL",
            limit=2,
            sources=["alpha_vantage"]
        )
        
        if news_data:
            print(f"  ✅ 获取到 {len(news_data)} 条新闻")
            for news in news_data:
                print(f"    - {news.title[:50]}... ({news.date})")
        else:
            print(f"  ❌ 该日期无新闻数据")
    
    # 恢复默认设置
    news_config.set_time_offset_days(1)


def demo_configuration_methods():
    """演示配置方法"""
    print("\n📰 演示4: 配置方法")
    print("=" * 50)
    
    from src.config.news_config import news_config
    
    # 保存原始配置
    original_sources = news_config.get_selected_sources().copy()
    original_offset = news_config.get_time_offset_days()
    original_use_local = news_config.is_using_local_data()
    
    print("⚙️  演示配置修改:")
    
    # 修改配置
    print("\n1. 修改新闻源为仅Alpha Vantage:")
    news_config.set_selected_sources(["alpha_vantage"])
    print(f"   当前源: {news_config.get_selected_sources()}")
    
    print("\n2. 修改时间偏移为2天:")
    news_config.set_time_offset_days(2)
    print(f"   当前偏移: {news_config.get_time_offset_days()} 天")
    
    print("\n3. 切换到API模式:")
    news_config.set_use_local_data(False)
    print(f"   当前模式: {'本地数据' if news_config.is_using_local_data() else 'API数据'}")
    
    # 恢复原始配置
    print("\n4. 恢复原始配置:")
    news_config.set_selected_sources(original_sources)
    news_config.set_time_offset_days(original_offset)
    news_config.set_use_local_data(original_use_local)
    print(f"   源: {news_config.get_selected_sources()}")
    print(f"   偏移: {news_config.get_time_offset_days()} 天")
    print(f"   模式: {'本地数据' if news_config.is_using_local_data() else 'API数据'}")


def demo_api_integration():
    """演示API集成"""
    print("\n📰 演示5: API集成")
    print("=" * 50)
    
    from src.tools.api import get_formatted_multi_source_news
    
    print("📡 演示格式化新闻获取:")
    
    # 获取格式化的新闻文本
    formatted_news = get_formatted_multi_source_news(
        ticker="AAPL",
        limit=3,
        sources=["alpha_vantage", "newsapi"],
        agent_name="demo_agent"
    )
    
    if formatted_news and formatted_news != "No news articles available for analysis.":
        print(f"✅ 成功获取格式化新闻")
        print(f"📝 新闻长度: {len(formatted_news)} 字符")
        
        # 显示新闻预览
        lines = formatted_news.split('\n')
        preview_lines = lines[:10]  # 显示前10行
        print(f"\n📄 新闻预览:")
        for line in preview_lines:
            print(f"  {line}")
        
        if len(lines) > 10:
            print(f"  ... (还有 {len(lines) - 10} 行)")
    else:
        print("❌ 未获取到格式化新闻")


def demo_data_format_compatibility():
    """演示数据格式兼容性"""
    print("\n📰 演示6: 数据格式兼容性")
    print("=" * 50)
    
    from src.tools.local_news_reader import get_local_multi_source_news
    from src.data.models import YahooFinanceNews
    
    print("🔍 验证数据格式兼容性:")
    
    # 获取新闻数据
    news_data = get_local_multi_source_news(
        ticker="AAPL",
        limit=1,
        sources=["alpha_vantage"]
    )
    
    if news_data:
        news_item = news_data[0]
        
        # 验证数据类型
        print(f"✅ 数据类型: {type(news_item).__name__}")
        print(f"✅ 是否为YahooFinanceNews: {isinstance(news_item, YahooFinanceNews)}")
        
        # 显示所有字段
        print(f"\n📋 数据字段:")
        print(f"  ticker: {news_item.ticker}")
        print(f"  title: {news_item.title[:50]}...")
        print(f"  summary: {news_item.summary[:50] if news_item.summary else 'None'}...")
        print(f"  content: {news_item.content[:50] if news_item.content else 'None'}...")
        print(f"  author: {news_item.author}")
        print(f"  source: {news_item.source}")
        print(f"  date: {news_item.date}")
        print(f"  url: {news_item.url[:50] if news_item.url else 'None'}...")
        print(f"  sentiment: {news_item.sentiment}")
        print(f"  thumbnail: {news_item.thumbnail}")
        
        print(f"\n✅ 所有字段都符合YahooFinanceNews模型规范")
    else:
        print("❌ 未获取到新闻数据进行验证")


def main():
    """主演示函数"""
    print("🚀 本地新闻系统使用演示")
    print("=" * 60)
    
    demos = [
        demo_basic_usage,
        demo_source_selection,
        demo_time_offset,
        demo_configuration_methods,
        demo_api_integration,
        demo_data_format_compatibility
    ]
    
    for i, demo_func in enumerate(demos, 1):
        try:
            demo_func()
            if i < len(demos):
                input(f"\n按回车键继续下一个演示...")
        except KeyboardInterrupt:
            print(f"\n\n👋 演示被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 演示 {i} 出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 演示完成！")
    print(f"\n📚 更多信息请参考:")
    print(f"  - LOCAL_NEWS_SYSTEM_README.md")
    print(f"  - news_config_example.json")
    print(f"  - test_local_news_system.py")


if __name__ == "__main__":
    main()
